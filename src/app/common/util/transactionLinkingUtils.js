/**
 * Utility functions for transaction linking functionality
 */

import { openModal } from "../modals/modalSlice";
import { partyIsAgent } from "./util";

/**
 * Check if a party should trigger transaction linking modal
 * @param {Object} party - The party object
 * @returns {boolean} - Whether linking modal should be shown
 */
export function shouldShowTransactionLinkingModal(party) {
  // Only for agents with email addresses
  if (!partyIsAgent(party.role) || !party.email) {
    return false;
  }

  // Don't show if party already declined linking or is already linked
  if (party.declinedLinking || party.isLinked) {
    return false;
  }

  // Only show if party is a TransActioner user
  if (!party.isUser) {
    return false;
  }

  return true;
}

/**
 * Check for transaction linking and show modal if appropriate
 * @param {Function} dispatch - Redux dispatch function
 * @param {Object} party - The party object
 * @param {Object} transaction - The transaction object
 * @param {number} delay - Delay in milliseconds before checking (default: 1000)
 */
export function checkForTransactionLinking(dispatch, party, transaction, delay = 1000) {
  if (!shouldShowTransactionLinkingModal(party)) {
    return;
  }

  // Wait for functionLinkUserToParty to complete and set isUser flag
  setTimeout(() => {
    try {
      // Re-check the party status after delay
      if (shouldShowTransactionLinkingModal(party)) {
        dispatch(
          openModal({
            modalType: "TransactionLinkingModal",
            modalProps: {
              party: party,
              transaction: transaction,
            },
          })
        );
      }
    } catch (error) {
      console.error("Error checking for transaction linking:", error);
    }
  }, delay);
}

/**
 * Check for transaction linking after MLS import
 * @param {Function} dispatch - Redux dispatch function
 * @param {Object} mlsData - The MLS data object
 * @param {Object} transaction - The transaction object
 * @param {Array} parties - Array of all parties
 */
export function checkForMlsTransactionLinking(dispatch, mlsData, transaction, parties) {
  if (!mlsData?.listingAgent?.email || transaction.agentRepresents !== "Buyer") {
    return;
  }

  // Find the newly added listing agent party
  const listingAgentParty = parties.find(
    (party) => party.role === "Listing Agent" && party.email === mlsData.listingAgent.email
  );

  if (listingAgentParty) {
    // Wait longer for MLS import to complete all operations
    checkForTransactionLinking(dispatch, listingAgentParty, transaction, 2000);
  }
}
