import React, { useState } from "react";
import { useDispatch } from "react-redux";
import { Confirm, Dropdown } from "semantic-ui-react";
import { useMediaQuery } from "react-responsive";
import { toast } from "react-toastify";
// import _ from "lodash";
import { openModal } from "../../app/common/modals/modalSlice";
import { deleteCeCertInDb } from "../../app/firestore/firestoreService";

export default function CeCertsActionButtons({ ceCert, actionButtonMode }) {
  const dispatch = useDispatch();

  const [confirmOpen, setConfirmOpen] = useState(false);
  const isMobile = useMediaQuery({ query: "(max-width:768px)" });

  function handleEdit() {
    dispatch(
      openModal({
        modalType: "CeCertsEdit",
        modalProps: { ceCert: ceCert },
      })
    );
  }

  async function handleDelete() {
    try {
      await deleteCeCertInDb(ceCert.id);
      toast.success("CE Cert successfully deleted");
    } catch (error) {
      toast.error(error.message);
    } finally {
    }
  }

  return (
    <div className={isMobile ? null : "text align right"}>
      <Dropdown
        button
        icon="chevron down"
        text={isMobile ? "Actions " : null}
        className={isMobile ? "button icon" : "button mini icon"}
        direction={isMobile ? "right" : "left"}
      >
        <Dropdown.Menu>
          <Dropdown.Item onClick={() => handleEdit()}>Edit</Dropdown.Item>
          <Dropdown.Item onClick={() => setConfirmOpen(true)}>
            Delete
          </Dropdown.Item>
        </Dropdown.Menu>
      </Dropdown>
      <Confirm
        open={confirmOpen}
        onCancel={() => setConfirmOpen(false)}
        onConfirm={() => handleDelete()}
      ></Confirm>
    </div>
  );
}
