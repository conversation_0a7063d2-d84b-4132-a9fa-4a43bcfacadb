import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Checkbox } from "semantic-ui-react";
import { useDispatch, useSelector } from "react-redux";
import { closeModal } from "../../../app/common/modals/modalSlice";
import {
  updateDocAddSharingInDb,
  updateTransAddSharingInDb,
  sendDocSharingEmail,
  addHistoryToDb,
  updateTransactionInDb
} from "../../../app/firestore/firestoreService";

export default function DocShareAgentNoClientsPrompt({ doc, party, transaction }) {
  const dispatch = useDispatch();
  const { currentUserProfile } = useSelector((state) => state.profile);
  const [dontShowAgain, setDontShowAgain] = useState(false);

  async function handleProceedAnyway() {
    dispatch(closeModal({ modalType: "DocShareAgentNoClientsPrompt" }));

    // If user checked "don't show again", update the transaction
    if (dontShowAgain) {
      try {
        await updateTransactionInDb(transaction.id, {
          hideAgentNoClientsPrompt: true
        });
      } catch (error) {
        console.error("Error updating transaction preference:", error);
      }
    }

    const agentProfile = transaction.agentProfile?.lastName ? transaction.agentProfile : currentUserProfile;

    try {
      updateDocAddSharingInDb(doc.id, party, transaction, null, false);
      updateTransAddSharingInDb(transaction.id, party, transaction);
      sendDocSharingEmail([party], "sharing", agentProfile, transaction);
      addHistoryToDb(transaction.id, currentUserProfile, "shared", doc.name, party);
    } catch (error) {
      console.error("Error sharing document:", error);
    }
  }

  function handleCancel() {
    dispatch(closeModal({ modalType: "DocShareAgentNoClientsPrompt" }));
  }

  const otherSideRole = transaction.agentRepresents === "Buyer" ? "Seller" : "Buyer";

  return (
    <Modal open={true} size="small">
      <Header icon="info circle" content="Add Client Names for Better Experience" />
      <Modal.Content>
        <p>
          <strong>To enable a smoother transaction process:</strong>
        </p>
        <p>
          If you add the names of the {otherSideRole.toLowerCase()} parties to your transaction's Parties section, 
          the forms will auto-populate with their names and the {party.role} (who is not a TransActioner subscriber) 
          will be able to send documents for signatures from their Client Portal.
        </p>
        <p>
          If the other agent sends to their clients for signatures, you will get an email when they sign and their activities will display in your History Log.
          This makes the transaction process much easier for everyone involved.
        </p>
        <p>
          <strong>Would you like to:</strong>
        </p>
        <ul>
          <li>Add the {otherSideRole.toLowerCase()} party names to your transaction in Parties first (recommended), or</li>
          <li>Proceed with sharing the document anyway?</li>
        </ul>
        <div style={{ marginTop: "20px", paddingTop: "15px", borderTop: "1px solid #e0e0e0" }}>
          <Checkbox
            checked={dontShowAgain}
            onChange={(e, { checked }) => setDontShowAgain(checked)}
            label="Don't show this message again on this transaction"
          />
        </div>
      </Modal.Content>
      <Modal.Actions>
        <Button color="grey" onClick={handleCancel}>
          Cancel - I'll Add Names First
        </Button>
        <Button color="blue" onClick={handleProceedAnyway}>
          Proceed Anyway
        </Button>
      </Modal.Actions>
    </Modal>
  );
}
