export function dualStatusDisclosureRealEstateBrokerMortgageBroker() {
return [
   {
      page: 0,
      name: "logo",
      isText: false,
      type: "logo",
      top: 28.673,
      left: 53,
      width: 253,
      height: 43.2
   }
   ,
   {
   page: 0,
   fontName: "Helvetica",
   fontSize: 10,
   type: "text",
   name: "mortgage broker in the State of Colorado", 
   top: 165.653,
   left: 137.28,
   width: 162.12,
   height: 13.867
}
,
{
   page: 0,
   type: "checkbox",
   name: "Broker Is acting", 
   top: 264.382,
   left: 129.194,
   width: 11.987,
   height: 11.987
}
,
{
   page: 0,
   type: "checkbox",
   name: "Broker Is Not acting", 
   top: 264.382,
   left: 155.832,
   width: 11.987,
   height: 11.987
}
,
{
   page: 0,
   type: "checkbox",
   name: "Broker Is accepting", 
   top: 278.384,
   left: 227.772,
   width: 11.987,
   height: 11.987
}
,
{
   page: 0,
   type: "checkbox",
   name: "Broker Is Not accepting", 
   top: 278.401,
   left: 251.763,
   width: 11.987,
   height: 11.987
}
,
{
   page: 0,
   fontName: "Helvetica",
   fontSize: 10,
   type: "text",
   name: "Does Not consent to the Broker acting in a dual capacity as a licensed real estate broker and a", 
   top: 330.467,
   left: 180.96,
   width: 312.12,
   height: 14.533
}
,
{
   page: 0,
   type: "checkbox",
   name: "Broker/Borrower Does", 
   top: 332.360,
   left: 498.847,
   width: 11.987,
   height: 11.987
}
,
{
   page: 0,
   type: "checkbox",
   name: "Broker/Borrower Does Not", 
   top: 346.959,
   left: 71.2565,
   width: 11.9871,
   height: 11.987
}
,
{
   page: 0,
   type: "checkbox",
   name: "Broker/Borrower Does 2", 
   top: 360.961,
   left: 395.591,
   width: 11.987,
   height: 11.987
}
,
{
   page: 0,
   type: "checkbox",
   name: "Broker/Borrower Does Not 2", 
   top: 360.979,
   left: 438.229,
   width: 11.987,
   height: 11.987
}
,
{
   page: 0,
   fontName: "Helvetica",
   fontSize: 10,
   type: "text",
   name: "receipt of this document on", 
   top: 508.295,
   left: 331.32,
   width: 90.12,
   height: 15.865
}
,
{
   page: 0,
   fontName: "Helvetica",
   fontSize: 10,
   type: "text",
   name: "On", 
   top: 605.561,
   left: 91.2,
   width: 96.0,
   height: 15.199
}
,
{
   page: 0,
   fontName: "Helvetica",
   fontSize: 10,
   type: "textarea",
   name: "Broker provided", 
   top: 604.229,
   left: 297.491,
   width: 150.000,
   height: 16.531
}
,
{
   page: 0,
   fontName: "Helvetica",
   fontSize: 10,
   type: "text",
   name: "document via", 
   top: 621.84,
   left: 240.96,
   width: 156.12,
   height: 12.72
}
