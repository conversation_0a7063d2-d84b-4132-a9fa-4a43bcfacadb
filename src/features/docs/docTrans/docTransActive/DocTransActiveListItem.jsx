import { formatDistance } from "date-fns";
import React from "react";
import { Icon, Popup, Table, Label } from "semantic-ui-react";
import { useDispatch, useSelector } from "react-redux";
import {
  convertFileTypeToIcon,
  convertFullName,
  convertPartiesAbleToShare,
  convertRoleToSharingDisplay,
  convertSharingWithToColor,
  dateFormatDeadlineWithTime,
  partyIsAgent,
  partyIsOtherAgent,
  truncateText,
  hasOtherSideClients,
} from "../../../../app/common/util/util";
import DocActionButtons from "../../docComponents/DocActionButtons";
import {
  addHistoryToDb,
  sendDocSharingEmail,
  sendOfferSubmittedEmail,
  updateDocAddSharingInDb,
  updateDocInDb,
  updateDocRemoveSharingInDb,
  updateTransAddSharingInDb,
} from "../../../../app/firestore/firestoreService";
import _ from "lodash";
import { openModal } from "../../../../app/common/modals/modalSlice";
import { functionShareWithAgent } from "../../../../app/firestore/functionsService";
import { serverTimestamp } from "firebase/firestore";

export default function DocTransActiveListItem({ doc }) {
  const dispatch = useDispatch();
  const { transaction, allParties } = useSelector((state) => state.transaction);
  const { currentUserProfile } = useSelector((state) => state.profile);

  // Function to get signature status for other side clients
  function getOtherSideClientSignatureStatus(party) {
    if (!partyIsOtherAgent(transaction, party) || !doc.signingDetails) {
      return [];
    }

    // Determine which roles are the other side clients based on this agent's representation
    let otherSideClientRoles = [];
    if (party.role === "Listing Agent") {
      // Listing agent's clients are Sellers
      otherSideClientRoles = ["Seller", "Seller 2", "Seller 3"];
    } else if (party.role === "Buyer Agent") {
      // Buyer agent's clients are Buyers
      otherSideClientRoles = ["Buyer", "Buyer 2", "Buyer 3"];
    }

    // Get signature status for each client role that exists in signingDetails
    const clientStatuses = [];
    otherSideClientRoles.forEach((role) => {
      const roleKey = role.toLowerCase().replace(" ", ""); // Convert "Seller 2" to "seller2"
      const signingDetail =
        doc.signingDetails[roleKey] || doc.signingDetails[role];

      if (signingDetail && signingDetail.sentOut) {
        const clientName =
          signingDetail.firstName && signingDetail.lastName
            ? `${signingDetail.firstName} ${signingDetail.lastName}`
            : role;
        const status = signingDetail.signed
          ? "signed"
          : "waiting for signature";
        clientStatuses.push(`${role}: ${status} (${clientName})`);
      }
    });

    return clientStatuses;
  }

  function sendOfferAlertEmailToAdmin(party, doc) {
    if (
      partyIsAgent(party.role) &&
      doc.title === "Contract to Buy and Sell, Residential"
    ) {
      sendOfferSubmittedEmail(currentUserProfile, party, transaction, doc);
    }
  }

  function handleSharingClick(party) {
    const partyRoleCamel = _.camelCase(party.role);
    const agentProfile = transaction.agentProfile?.lastName
      ? transaction.agentProfile
      : currentUserProfile;
    const sharedByName = agentProfile.firstName + " " + agentProfile.lastName;
    if (doc.sharingWithRole?.[partyRoleCamel]) {
      try {
        updateDocRemoveSharingInDb(doc.id, party, transaction, allParties);
        addHistoryToDb(
          transaction.id,
          currentUserProfile,
          "unshared",
          doc.name,
          party
        );
      } catch (error) {
        throw error;
      }
    } else if (
      partyIsAgent(party.role) &&
      party.isUser &&
      !party.isLinked &&
      !party.declinedLinking
    ) {
      dispatch(
        openModal({
          modalType: "DocShareAgentLinking",
          modalProps: { doc: doc, party: party, transaction: transaction },
        })
      );
    } else {
      // Check if sharing with a non-user agent and no other-side clients exist
      if (
        partyIsAgent(party.role) &&
        !party.isUser &&
        !hasOtherSideClients(allParties, transaction.agentRepresents) &&
        !transaction.hideAgentNoClientsPrompt
      ) {
        dispatch(
          openModal({
            modalType: "DocShareAgentNoClientsPrompt",
            modalProps: { doc: doc, party: party, transaction: transaction },
          })
        );
        return;
      }

      try {
        // Temporarily disable client restoration to debug basic sharing
        const restoreAgentClients = false; // partyIsAgent(party.role);
        updateDocAddSharingInDb(
          doc.id,
          party,
          transaction,
          allParties,
          restoreAgentClients
        );
        updateTransAddSharingInDb(transaction.id, party, transaction);
      } catch (error) {
        throw error;
      }
      if (partyIsAgent(party.role) && party.isUser && party.isLinked) {
        updateDocInDb(
          doc.id,
          {
            sentToAgent: true,
            sentToAgentId: party.id,
            sentDocIsDirty: false,
            sentToAgentAt: serverTimestamp(),
          },
          true
        );
        functionShareWithAgent({
          doc: doc,
          party: party,
          sharedBy: sharedByName,
        });
      }
      sendDocSharingEmail([party], "sharing", agentProfile, transaction);
      sendOfferAlertEmailToAdmin(party, doc);
      addHistoryToDb(
        transaction.id,
        currentUserProfile,
        "shared",
        doc.name,
        party
      );
    }
  }

  return (
    <Table.Row key={doc.id}>
      <Table.Cell>
        <Icon
          name={`file ${convertFileTypeToIcon(doc)} outline`}
          size="large"
        />
        &nbsp;
        {truncateText(doc.name, 60)}
      </Table.Cell>
      <Table.Cell textAlign="center">
        {doc.signingComplete && (
          <Icon color="green" name="check" size="large" />
        )}
      </Table.Cell>

      {doc.shared && <Table.Cell>{doc.sharedBy}</Table.Cell>}
      <Table.Cell>
        {convertPartiesAbleToShare(allParties, transaction.agentRepresents).map(
          (party) => (
            <React.Fragment key={party.role}>
              {party.email && (
                <Popup
                  trigger={
                    <Label
                      key={party.role}
                      style={{ cursor: "pointer", marginBottom: "2px" }}
                      color={convertSharingWithToColor(doc, party)}
                      onClick={() => handleSharingClick(party)}
                    >
                      {convertRoleToSharingDisplay(party.role)}
                    </Label>
                  }
                >
                  <p className="tiny bottom margin">
                    <span className="bold text blue">{party.role}:</span>{" "}
                    {convertFullName(party)}
                    <br/>
                    {party.email}
                  </p>
                  {partyIsAgent(party.role) &&
                    party.hasTransactionCoordinator &&
                    party.transactionCoordinator?.firstName &&
                    party.transactionCoordinator?.lastName &&
                    party.transactionCoordinator?.email && (
                      <p className="tiny bottom margin">
                        <span className="bold text blue">TC:</span>{" "}
                        {convertFullName(party.transactionCoordinator)}
                      </p>
                    )}
                  {partyIsAgent(party.role) &&
                    party.isUser &&
                    party.isLinked &&
                    doc.sentToAgentAt && (
                      <p>
                        <span className="bold text blue">Sent out</span>{" "}
                        {dateFormatDeadlineWithTime(doc.sentToAgentAt)}
                      </p>
                    )}
                  {(() => {
                    const clientStatuses =
                      getOtherSideClientSignatureStatus(party);
                    return (
                      clientStatuses.length > 0 && (
                        <div>
                          <p className="tiny bottom margin">
                            <span className="bold text blue">
                              Client Status:
                            </span>
                          </p>
                          {clientStatuses.map((status, index) => (
                            <p
                              key={index}
                              className="tiny bottom margin"
                              style={{ marginLeft: "10px" }}
                            >
                              {status}
                            </p>
                          ))}
                        </div>
                      )
                    );
                  })()}
                </Popup>
              )}
            </React.Fragment>
          )
        )}
      </Table.Cell>
      <Table.Cell>
        <p>
          {doc.updatedAt && doc.updatedAt instanceof Date
            ? formatDistance(doc.updatedAt, new Date(), { addSuffix: true })
            : ""}
        </p>
      </Table.Cell>
      <Table.Cell>
        <DocActionButtons doc={doc} />
      </Table.Cell>
    </Table.Row>
  );
}
