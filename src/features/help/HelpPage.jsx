import React, { useState, useRef, useEffect } from "react";
import { useSelector } from "react-redux";
import {
  Container,
  Header,
  Segment,
  Grid,
  Input,
  Button,
  Icon,
  Divider,
  Message,
  Loader,
  Card,
} from "semantic-ui-react";
import { convertAddressFull } from "../../app/common/util/util";
import {
  getHelpQuestionsFromDb,
  sendHelpQuestionEmail,
} from "../../app/firestore/firestoreService";

// Component for expandable text display with media support
function ExpandableAnswer({ answer, maxLines = 3 }) {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpanded = () => setIsExpanded(!isExpanded);

  // Parse content for images and videos
  const parseContent = (content) => {
    if (!content) return content;

    // Replace image markdown syntax: ![alt](url)
    const imageRegex = /!\[([^\]]*)\]\(([^)]+)\)/g;
    content = content.replace(imageRegex, (_, alt, url) => {
      return `<img src="${url}" alt="${alt}" style="max-width: 100%; height: auto; margin: 10px 0; border-radius: 4px;" />`;
    });

    // Replace YouTube links with embeds: [youtube](video_id) or [youtube](full_url)
    const youtubeRegex = /\[youtube\]\(([^)]+)\)/g;
    content = content.replace(youtubeRegex, (_, videoId) => {
      // Extract video ID from full URL if needed
      let id = videoId;
      const youtubeUrlMatch = videoId.match(
        /(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/
      );
      if (youtubeUrlMatch) {
        id = youtubeUrlMatch[1];
      }

      return `<div style="margin: 15px 0;">
        <iframe
          width="100%"
          height="315"
          src="https://www.youtube.com/embed/${id}"
          frameborder="0"
          allowfullscreen
          style="border-radius: 4px; max-width: 560px;"
        ></iframe>
      </div>`;
    });

    return content;
  };

  const processedContent = parseContent(answer);

  // Check if content needs expansion (rough estimate)
  const needsExpansion =
    answer &&
    (answer.split("\n").length > maxLines ||
      answer.length > 200 ||
      answer.includes("![") ||
      answer.includes("[youtube]"));

  const previewContent =
    !isExpanded && needsExpansion
      ? answer.split("\n").slice(0, maxLines).join("\n") + "..."
      : processedContent;

  return (
    <div style={{ marginBottom: "1em" }}>
      <div
        style={{
          whiteSpace: "pre-line",
          lineHeight: "1.5em",
        }}
      >
        {!isExpanded && needsExpansion ? (
          <div>{previewContent}</div>
        ) : (
          <div dangerouslySetInnerHTML={{ __html: processedContent }} />
        )}
      </div>
      {needsExpansion && (
        <Button
          basic
          size="mini"
          onClick={toggleExpanded}
          style={{ marginTop: "0.5em", padding: "0.3em 0.8em" }}
        >
          {isExpanded ? "Show Less" : "Show More"}
        </Button>
      )}
    </div>
  );
}

// Component for displaying multiple search results
function SearchResults({ results }) {
  if (!results || results.length === 0) return null;

  if (results.length === 1) {
    return (
      <div>
        <Header as="h4" color="blue" style={{ marginBottom: "0.5em" }}>
          {results[0].question}
        </Header>
        <ExpandableAnswer answer={results[0].answer} />
      </div>
    );
  }

  return (
    <div>
      <Header as="h4" color="blue" style={{ marginBottom: "1em" }}>
        Found {results.length} potential answers:
      </Header>
      {results.map((result, index) => (
        <Card key={index} fluid style={{ marginBottom: "1em" }}>
          <Card.Content>
            <Card.Header style={{ fontSize: "1.1em", color: "#2185d0" }}>
              {result.question}
            </Card.Header>
            <Card.Description style={{ marginTop: "0.5em" }}>
              <ExpandableAnswer answer={result.answer} maxLines={3} />
            </Card.Description>
          </Card.Content>
        </Card>
      ))}
    </div>
  );
}

export default function HelpPage() {
  const { transaction } = useSelector((state) => state.transaction);
  const { currentUserProfile } = useSelector((state) => state.profile);

  const [question, setQuestion] = useState("");
  const [userAnswer, setUserAnswer] = useState("");
  const [messages, setMessages] = useState([
    {
      type: "system",
      content:
        "Welcome to TransActioner Help! Ask any question about TransActioner or your current transaction.",
    },
  ]);
  const [isLoading, setIsLoading] = useState(false);
  const [helpData, setHelpData] = useState([]);
  const [isLoadingHelpData, setIsLoadingHelpData] = useState(true);

  const messagesEndRef = useRef(null);
  const messagesStartRef = useRef(null);

  // Load help data from Firestore
  useEffect(() => {
    const fetchHelpData = async () => {
      try {
        setIsLoadingHelpData(true);
        const data = await getHelpQuestionsFromDb();
        setHelpData(data);
      } catch (error) {
        console.error("Error fetching help data:", error);
        setMessages((prev) => [
          ...prev,
          {
            type: "system",
            content:
              "There was an error loading help content. Please try again later.",
          },
        ]);
      } finally {
        setIsLoadingHelpData(false);
      }
    };

    fetchHelpData();
  }, []);

  useEffect(() => {
    // return scrollToBottom();
    return scrollToUserMessageStart();
  }, [messages, messagesStartRef]);

  // const scrollToBottom = () => {
  //   return messagesEndRef?.current?.scrollIntoView({ behavior: "smooth" });
  // };
  const scrollToUserMessageStart = () => {
    return messagesStartRef?.current?.scrollIntoView({ behavior: "smooth" });
  };

  // Find multiple matching questions from our help data
  const findMatches = (userQuestion) => {
    if (!helpData || helpData.length === 0) return [];

    const userQuestionLower = userQuestion.toLowerCase();
    const matches = [];

    // First try to find exact matches
    const exactMatches = helpData.filter(
      (item) =>
        item.question.toLowerCase() === userQuestionLower ||
        item.keywords?.some((keyword) =>
          userQuestionLower.includes(keyword.toLowerCase())
        )
    );

    if (exactMatches.length > 0) {
      return exactMatches.slice(0, 3); // Return up to 3 exact matches
    }

    // If no exact match, find the closest matches
    helpData.forEach((item) => {
      const questionWords = item.question.toLowerCase().split(/\s+/);
      const userWords = userQuestionLower.split(/\s+/);

      let matchScore = 0;
      userWords.forEach((word) => {
        if (word.length > 2 && questionWords.includes(word)) {
          matchScore++;
        }
      });

      // Check keywords too
      if (item.keywords) {
        item.keywords.forEach((keyword) => {
          if (userQuestionLower.includes(keyword.toLowerCase())) {
            matchScore += 2; // Keywords are more important
          }
        });
      }

      if (matchScore > 0) {
        matches.push({ ...item, score: matchScore });
      }
    });

    // Sort by score and return top matches
    return matches
      .sort((a, b) => b.score - a.score)
      .slice(0, 5)
      .map(({ score, ...item }) => item);
  };

  // Replace placeholders in the answer with transaction data
  const replaceTransactionPlaceholders = (answer) => {
    if (!transaction) return answer;

    let processedAnswer = answer;

    // Replace transaction placeholders
    const placeholders = {
      "{CLIENT_NAME}": transaction.client
        ? `${transaction.client.firstName || ""} ${
            transaction.client.lastName || ""
          }`.trim()
        : "your client",
      "{TRANSACTION_ADDRESS}": transaction.address
        ? convertAddressFull(transaction.address)
        : "this transaction",
      "{TRANSACTION_STATUS}": transaction.status || "current status",
      "{AGENT_NAME}": currentUserProfile
        ? `${currentUserProfile.firstName || ""} ${
            currentUserProfile.lastName || ""
          }`.trim()
        : "your agent",
    };

    Object.entries(placeholders).forEach(([placeholder, value]) => {
      processedAnswer = processedAnswer.replace(
        new RegExp(placeholder, "g"),
        value
      );
    });

    return processedAnswer;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!question.trim()) return;

    // Add user question to messages
    const userQuestion = question;
    setMessages((prev) => [...prev, { type: "user", content: userQuestion }]);
    setQuestion("");
    setUserAnswer("");
    setIsLoading(true);

    // Simulate a short delay for better UX
    setTimeout(() => {
      // Find matching questions in our help database
      const matches = findMatches(userQuestion);

      if (matches.length > 0) {
        // We found matching questions - create a response with SearchResults
        const response = {
          type: "assistant",
          content: null,
          matches: matches.map((match) => ({
            ...match,
            answer: replaceTransactionPlaceholders(match.answer),
          })),
        };

        setMessages((prev) => [...prev, response]);
        setUserAnswer("Found matching help content");
      } else if (transaction?.client?.lastName) {
        // No match but we have transaction context
        const response = `I don't have a specific answer for that question. Here's some information about your current transaction:

      Address: ${
        transaction.address
          ? convertAddressFull(transaction.address)
          : "Not specified"
      }
      Status: ${transaction.status || "Not specified"}

      For more specific help, please try rephrasing your question or contact support.`;

        setMessages((prev) => [
          ...prev,
          { type: "assistant", content: response },
        ]);
        setUserAnswer(response);
      } else {
        // No match and no transaction context
        const response =
          "I don't have a specific answer for that question. Please try rephrasing or ask another question about using TransActioner.";

        setMessages((prev) => [
          ...prev,
          { type: "assistant", content: response },
        ]);
        setUserAnswer(response);
      }
      setIsLoading(false);
    }, 1000);

    // Send the user's <NAME_EMAIL>
    try {
      // Only send email if we're in production environment
      sendHelpQuestionEmail(
        userQuestion,
        userAnswer,
        currentUserProfile,
        transaction
      );
    } catch (error) {
      //console.error("Error sending help question email:", error);
      // Don't show error to user, just log it
    }
  };

  return (
    <div className="main-page-wrapper">
      <Container>
        <Grid centered stackable>
          <Grid.Column width={14}>
            <Header as="h1" color="blue">
              <Icon size="small" name="question circle" />
              <Header.Content>
                TransActioner Help
                {transaction?.client && (
                  <Header.Subheader>
                    Current Transaction:
                    {transaction.title?.length > 2 ? transaction.title : ""}
                    {transaction.address?.street
                      ? convertAddressFull(transaction.address)
                      : ""}
                    {transaction.client?.lastName
                      ? " - " +
                        transaction.client?.firstName +
                        " " +
                        transaction.client?.lastName
                      : ""}
                  </Header.Subheader>
                )}
              </Header.Content>
            </Header>
            <span>
              Please feel free to reach out via call or text at 970.412.1020 or
              <NAME_EMAIL> so we can help!
            </span>
            <span>
              <br />
              These are the most frequently asked questions with answers.
            </span>

            <Segment
              style={{
                height: "60vh",
                display: "flex",
                flexDirection: "column",
              }}
            >
              <form onSubmit={handleSubmit}>
                <Input
                  fluid
                  placeholder="Type your question here..."
                  value={question}
                  onChange={(e) => setQuestion(e.target.value)}
                  action={
                    <Button
                      color="blue"
                      icon="send"
                      type="submit"
                      loading={isLoading}
                      disabled={isLoading || !question.trim()}
                    />
                  }
                />
              </form>
              <Divider />
              <div
                style={{
                  flex: 1,
                  overflowY: "auto",
                  padding: "1em",
                  marginBottom: "1em",
                }}
              >
                {isLoadingHelpData && messages.length === 1 ? (
                  <div style={{ textAlign: "center", padding: "2em" }}>
                    <Loader
                      active
                      inline="centered"
                      content="Loading help content..."
                    />
                  </div>
                ) : (
                  messages.map((msg, index) => (
                    <div key={index}>
                      <div ref={messagesStartRef} />
                      <Message
                        key={index}
                        color={
                          msg.type === "user"
                            ? "blue"
                            : msg.type === "system"
                            ? "grey"
                            : null
                        }
                        floating
                        style={{
                          maxWidth: "85%",
                          marginLeft: msg.type === "user" ? "auto" : "0",
                          marginRight: msg.type === "user" ? "0" : "auto",
                          marginBottom: "1em",
                          whiteSpace: msg.matches ? "normal" : "pre-line",
                        }}
                      >
                        {msg.matches ? (
                          <SearchResults results={msg.matches} />
                        ) : (
                          msg.content
                        )}
                      </Message>
                    </div>
                  ))
                )}
                <div ref={messagesEndRef} />
              </div>
            </Segment>
          </Grid.Column>
        </Grid>
      </Container>
    </div>
  );
}
