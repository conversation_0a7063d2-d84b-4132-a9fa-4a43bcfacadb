// Test for HelpPage functionality - focusing on helper functions
import { getHelpQuestionsFromDb } from '../../../app/firestore/firestoreService';

// Mock the firestore service
jest.mock('../../../app/firestore/firestoreService', () => ({
  getHelpQuestionsFromDb: jest.fn(() => Promise.resolve([
    {
      id: '1',
      question: 'How do I upload documents?',
      answer: 'You can upload documents by clicking the upload button.\n\nFor images, use this syntax: ![Image description](https://example.com/image.jpg)\n\nFor videos, use: [youtube](dQw4w9WgXcQ)',
      keywords: ['upload', 'documents', 'files']
    },
    {
      id: '2',
      question: 'How do I sign documents?',
      answer: 'To sign documents, navigate to the signing page and follow the instructions.',
      keywords: ['sign', 'signature', 'documents']
    }
  ])),
  sendHelpQuestionEmail: jest.fn(() => Promise.resolve())
}));

describe('HelpPage Functionality', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('can fetch help data from database', async () => {
    const helpData = await getHelpQuestionsFromDb();

    expect(helpData).toHaveLength(2);
    expect(helpData[0].question).toBe('How do I upload documents?');
    expect(helpData[0].keywords).toContain('upload');
  });

  test('image markdown syntax is processed correctly', () => {
    const answer = 'Here is an image: ![Test Image](https://example.com/test.jpg)';
    const imageRegex = /!\[([^\]]*)\]\(([^)]+)\)/g;
    const processedAnswer = answer.replace(imageRegex, (_, alt, url) => {
      return `<img src="${url}" alt="${alt}" style="max-width: 100%; height: auto; margin: 10px 0; border-radius: 4px;" />`;
    });

    expect(processedAnswer).toContain('<img src="https://example.com/test.jpg"');
    expect(processedAnswer).toContain('alt="Test Image"');
    expect(processedAnswer).toContain('max-width: 100%');
  });

  test('YouTube embed syntax is processed correctly', () => {
    const answer = 'Watch this video: [youtube](dQw4w9WgXcQ)';
    const youtubeRegex = /\[youtube\]\(([^)]+)\)/g;
    const processedAnswer = answer.replace(youtubeRegex, (_, videoId) => {
      return `<iframe src="https://www.youtube.com/embed/${videoId}" width="100%" height="315"></iframe>`;
    });

    expect(processedAnswer).toContain('<iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ"');
    expect(processedAnswer).toContain('width="100%"');
    expect(processedAnswer).toContain('height="315"');
  });

  test('YouTube URL is extracted correctly from full URL', () => {
    const fullUrl = 'https://www.youtube.com/watch?v=dQw4w9WgXcQ';
    const youtubeUrlMatch = fullUrl.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/);

    expect(youtubeUrlMatch).toBeTruthy();
    expect(youtubeUrlMatch[1]).toBe('dQw4w9WgXcQ');
  });

  test('content expansion logic works correctly', () => {
    const shortAnswer = 'Short answer';
    const longAnswer = 'This is a very long answer\nwith multiple lines\nand lots of content\nthat should be expandable';
    const imageAnswer = 'Answer with ![image](url)';
    const videoAnswer = 'Answer with [youtube](id)';

    // Short content should not need expansion
    expect(shortAnswer.split('\n').length <= 3 && shortAnswer.length <= 200).toBe(true);

    // Long content should need expansion
    expect(longAnswer.split('\n').length > 3 || longAnswer.length > 200).toBe(true);

    // Content with media should need expansion
    expect(imageAnswer.includes('![')).toBe(true);
    expect(videoAnswer.includes('[youtube]')).toBe(true);
  });

  test('search matching algorithm works correctly', () => {
    const helpData = [
      { question: 'How do I upload documents?', keywords: ['upload', 'documents'] },
      { question: 'How do I sign documents?', keywords: ['sign', 'signature'] },
      { question: 'How do I download files?', keywords: ['download', 'files'] }
    ];

    const userQuestion = 'upload documents';
    const userWords = userQuestion.toLowerCase().split(/\s+/);

    const matches = helpData.filter(item => {
      const questionWords = item.question.toLowerCase().split(/\s+/);
      let matchScore = 0;

      userWords.forEach(word => {
        if (word.length > 2 && questionWords.includes(word)) {
          matchScore++;
        }
      });

      if (item.keywords) {
        item.keywords.forEach(keyword => {
          if (userQuestion.toLowerCase().includes(keyword.toLowerCase())) {
            matchScore += 2;
          }
        });
      }

      return matchScore > 0;
    });

    expect(matches).toHaveLength(1);
    expect(matches[0].question).toBe('How do I upload documents?');
  });
});
