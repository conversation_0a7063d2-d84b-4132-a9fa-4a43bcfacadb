import React from "react";
import { useSelector } from "react-redux";
import { Grid } from "semantic-ui-react";
import ManagerAgentsFinancialsList from "./ManagerAgentsFinancialsList";

export default function ManagerFinancialsAgentDashboard() {
  const {
    // transActiveForManager,
    // transUnderContractForManager,
    // transActiveListingForManager,
    // transActiveBuyerForManager,
    transClosedForManager,
  } = useSelector((state) => state.transaction);

  let closedTransactionsByAgent = {};
  transClosedForManager.forEach((transaction) => {
    if (
      transaction.agentProfile?.firstName &&
      transaction.agentProfile?.lastName
    ) {
      let salesPrice = 0;
      if (transaction.salesPrice) {
        salesPrice = parseFloat(transaction.salesPrice.replace(/[$,]/g, ""));
      }
      const agentName = `${transaction.agentProfile?.firstName} ${transaction.agentProfile?.lastName}`;
      if (closedTransactionsByAgent[agentName]) {
        closedTransactionsByAgent[agentName].count += 1;
        closedTransactionsByAgent[agentName].totalSalesPrice += salesPrice;
      } else {
        closedTransactionsByAgent[agentName] = {
          count: 1,
          totalSalesPrice: salesPrice,
        };
      }
    }
  });

  // let numberUnderContract = "0";
  // if (
  //   transUnderContractForManager?.length > 0
  //   // && transActiveForManager?.length > 0
  // ) {
  //   numberUnderContract = (transUnderContractForManager.length
  //   ).toString();
  // }

  return (
    <div className="main-page-wrapper">
      <>
        <Grid stackable className="large bottom margin">
          <Grid.Row>
            <Grid.Column
              computer={16}
              className="large top margin small bottom margin"
            >
              <h1
                className="zero bottom margin"
                style={{ position: "absolute", bottom: "0" }}
              >
                Manager Financials Dashboard
              </h1>
            </Grid.Column>
          </Grid.Row>
        </Grid>
        <Grid>
          <Grid.Column computer={16} className="small bottom margin">
            <ManagerAgentsFinancialsList></ManagerAgentsFinancialsList>
          </Grid.Column>
        </Grid>
        {/* <Grid>
                  <Grid.Column computer={16} className="small bottom margin">
                    <h3 className="large bottom margin">Upcoming / Recent Closings</h3>
                    <ManagerAgentsUpcomingClosingsList transactions={transActiveForManager} />
                  </Grid.Column>
                </Grid> */}
        {/* <Grid>
          <Grid.Column computer={16} className="small bottom margin">
            <h3 className="large bottom margin">Upcoming Closings</h3>
            <Table compact>
              <Table.Header className="mobile hidden">
                <Table.Row className="small-header">
                  <Table.HeaderCell>Agent</Table.HeaderCell>
                  <Table.HeaderCell>Number of Closings</Table.HeaderCell>
                  <Table.HeaderCell>Total Sales Price</Table.HeaderCell>
                </Table.Row>
              </Table.Header>
              <Table.Body>
                {transUnderContractForManager && Object.entries(transUnderContractForManager).map(
                  ([key, value]) => (
                    <Table.Row key={key}>
                      <Table.Cell>{key}</Table.Cell>
                      <Table.Cell>{value.count}</Table.Cell>
                      <Table.Cell>
                        {value.totalSalesPrice?.toLocaleString("en-US", {
                          style: "currency",
                          currency: "USD",
                          minimumFractionDigits: 0,
                          maximumFractionDigits: 0,
                        })}
                      </Table.Cell>
                    </Table.Row>
                  )
                )}
              </Table.Body>
            </Table>
          </Grid.Column>
        </Grid> */}
      </>
    </div>
  );
}
