import React, { useState } from "react";
import { Grid, Table, Button, Input, Dropdown } from "semantic-ui-react";
import _ from "lodash";
import { startOfDay, subDays } from "date-fns";
import { useSelector } from "react-redux";
import { Link } from "react-router-dom";
import ManagerTransactionsListItem from "./ManagerTransactionsListItem";
import { searchFilter } from "../../../app/common/util/util";

export default function ManagerNewBuyersList() {
  const [searchTerms, setSearchTerms] = useState("");
  const [selectedDays, setSelectedDays] = useState(7);
  const { currentUserProfile } = useSelector((state) => state.profile);

  const daysOptions = [
    { key: 3, text: "3 days", value: 3 },
    { key: 7, text: "7 days", value: 7 },
    { key: 14, text: "14 days", value: 14 },
    { key: 31, text: "31 days", value: 31 },
  ];

  // function searchByValue(items, searchTerms)
  // {
  //   return searchTerms ? items.filter((obj) => {
  //     return Object.values(obj).some(val => {
  //       return val.toString().toLowerCase().includes(searchTerms.toLowerCase());
  //     })
  //   }) : items;
  // }

  // return searchTerms
  //   ? items.filter((item) => {
  //       return Object.values(item)
  //         .join("")
  //         .toLowerCase()
  //         .includes(searchTerms.toLowerCase());
  //     })
  //   : items;

  const {
    // transActiveForManager,
    // transUnderContractForManager,
    // transActiveListingForManager,
    transActiveBuyerForManager,
    // transClosedForManager,
  } = useSelector((state) => state.transaction);
  const filteredTransactions = transActiveBuyerForManager.filter(
    (transaction) =>
      transaction.createdAt &&
      transaction.createdAt > startOfDay(subDays(new Date(), selectedDays))
  );
  const searchedTransactions = searchFilter(filteredTransactions, searchTerms);
  const sortedTransactions = _.orderBy(
    searchedTransactions,
    "createdAt",
    "desc"
  );

  //   if (!filteredTransactions || filteredTransactions.length === 0) return null;

  return (
    <>
      <div className="main-page-wrapper">
        <div>
          <Grid stackable className="large bottom margin">
            <Grid.Row>
              <Grid.Column
                computer={16}
                className="large top margin small bottom margin"
              >
                <h1
                  className="zero bottom margin"
                  style={{ position: "absolute", bottom: "0" }}
                >
                  Manager Transactions
                </h1>
              </Grid.Column>
            </Grid.Row>
            <Grid.Row>
              <Grid.Column computer={4}>
                <Input
                  type="text"
                  fluid
                  placeholder="Search"
                  value={searchTerms}
                  onChange={(e) => setSearchTerms(e.target.value)}
                ></Input>
              </Grid.Column>

              <Grid.Column computer={3}>
                <Dropdown
                  fluid
                  selection
                  options={daysOptions}
                  value={selectedDays}
                  onChange={(e, { value }) => setSelectedDays(value)}
                  placeholder="Select days"
                />
              </Grid.Column>

              <Grid.Column computer={3} tablet={4}>
                <Button.Group fluid size="small">
                  <Button as={Link} to="/managerUpcomingClosings">
                    Upcoming Closings
                  </Button>
                  <Button as={Link} to={`/managerRecentClosings/`}>
                    Recent Closings
                  </Button>
                  <Button as={Link} to={`/managerNewListings/`}>
                    New Listings
                  </Button>
                  <Button active as={Link} to={`/managerNewBuyers/`}>
                    New Buyers
                  </Button>
                </Button.Group>
              </Grid.Column>
            </Grid.Row>
            <Grid.Row>
              <Grid.Column
                computer={16}
                className="large top margin small bottom margin"
              >
                <h2
                  className="zero bottom margin"
                  style={{ position: "absolute", bottom: "0" }}
                >
                  New Buyers (past {selectedDays} days)
                </h2>
              </Grid.Column>
            </Grid.Row>
            {currentUserProfile?.managerDetails
              ?.hasAccessToManagerTransactionLists ? (
              <Grid.Row>
                <Table compact>
                  <Table.Header className="mobile hidden">
                    <Table.Row className="small-header">
                      <Table.HeaderCell></Table.HeaderCell>
                      <Table.HeaderCell>Created</Table.HeaderCell>
                      <Table.HeaderCell>Agent</Table.HeaderCell>
                      <Table.HeaderCell>
                        Client&nbsp;&nbsp;(Primary)
                      </Table.HeaderCell>
                      <Table.HeaderCell>
                        Client&nbsp;&nbsp;(Secondary)
                      </Table.HeaderCell>
                      <Table.HeaderCell>Representing</Table.HeaderCell>
                      <Table.HeaderCell>Address</Table.HeaderCell>
                    </Table.Row>
                  </Table.Header>
                  <Table.Body>
                    {sortedTransactions.map((transaction) => (
                      <ManagerTransactionsListItem
                        transaction={transaction}
                        key={transaction.id}
                        useDate={transaction.createdAt}
                      />
                    ))}
                  </Table.Body>
                </Table>
              </Grid.Row>
            ) : (
              <Grid.Row>
                <p>
                  Contact TransActioner to subscribe to Manager Transaction
                  Lists.
                </p>
              </Grid.Row>
            )}
          </Grid>
        </div>
      </div>
    </>
  );
}
