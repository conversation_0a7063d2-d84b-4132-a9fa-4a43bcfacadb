import React, { useState } from "react";
import { Grid, Table, Button, Popup, Icon, Input, Dropdown } from "semantic-ui-react";
import _ from "lodash";
import { startOfDay, subDays } from "date-fns";
import { useSelector } from "react-redux";
import { Link } from "react-router-dom";
import ManagerTransactionsListItem from "./ManagerTransactionsListItem";
import { searchFilter } from "../../../app/common/util/util";

export default function ManagerRecentClosingsList() {
  const [searchTerms, setSearchTerms] = useState("");
  const [selectedDays, setSelectedDays] = useState(7);
  const { currentUserProfile } = useSelector((state) => state.profile);

  const daysOptions = [
    { key: 3, text: "3 days", value: 3 },
    { key: 7, text: "7 days", value: 7 },
    { key: 14, text: "14 days", value: 14 },
    { key: 31, text: "31 days", value: 31 },
  ];

  const {
    // transActiveForManager,
    // transUnderContractForManager,
    // transActiveListingForManager,
    // transActiveBuyerForManager,
    transClosedForManager,
  } = useSelector((state) => state.transaction);
  const filteredTransactions = transClosedForManager.filter(
    (transaction) =>
      transaction.closingDateTime &&
      transaction.closingDateTime >
        startOfDay(subDays(new Date(), selectedDays))
  );
  const searchedTransactions = searchFilter(filteredTransactions, searchTerms);

  const sortedTransactions = _.orderBy(
    searchedTransactions,
    "closingDateTime",
    "desc"
  );

  //   if (!filteredTransactions || filteredTransactions.length === 0) return null;

  return (
    <>
      <div className="main-page-wrapper">
        <div>
          <Grid stackable className="large bottom margin">
            <Grid.Row>
              <Grid.Column
                computer={16}
                className="large top margin small bottom margin"
              >
                <h1
                  className="zero bottom margin"
                  style={{ position: "absolute", bottom: "0" }}
                >
                  Manager Transactions
                </h1>
              </Grid.Column>
            </Grid.Row>
            <Grid.Row>
              <Grid.Column computer={4}>
                <Input
                  type="text"
                  fluid
                  placeholder="Search"
                  value={searchTerms}
                  onChange={(e) => setSearchTerms(e.target.value)}
                ></Input>
              </Grid.Column>

              <Grid.Column computer={3}>
                <Dropdown
                  fluid
                  selection
                  options={daysOptions}
                  value={selectedDays}
                  onChange={(e, { value }) => setSelectedDays(value)}
                  placeholder="Select days"
                />
              </Grid.Column>

              <Grid.Column computer={3} tablet={4}>
                <Button.Group fluid size="small">
                  <Button as={Link} to="/managerUpcomingClosings">
                    Upcoming Closings
                  </Button>
                  <Button active as={Link} to={`/managerRecentClosings/`}>
                    Recent Closings
                  </Button>
                  <Button as={Link} to={`/managerNewListings/`}>
                    New Listings
                  </Button>
                  <Button as={Link} to={`/managerNewBuyers/`}>
                    New Buyers
                  </Button>
                </Button.Group>
              </Grid.Column>
            </Grid.Row>
            <Grid.Row>
              <Grid.Column
                computer={16}
                className="large top margin small bottom margin"
              >
                <h2
                  className="zero bottom margin"
                  style={{ position: "absolute", bottom: "0" }}
                >
                  Recent Closings (past {selectedDays} days)
                  <Popup
                    size="large"
                    trigger={
                      <Icon
                        name="info"
                        color="blue"
                        circular
                        inverted
                        size="mini"
                        style={{ marginLeft: "5px", marginBottom: "6px" }}
                      />
                    }
                  >
                    <p className="blue text mini bottom margin">
                      <i>
                        Transaction must have the status &nbsp;
                        <b>Complete</b> and <b>Closing Date</b> set in
                        Transaction Overview.
                      </i>
                    </p>
                  </Popup>
                </h2>
              </Grid.Column>
            </Grid.Row>
            {currentUserProfile?.managerDetails
              ?.hasAccessToManagerTransactionLists ? (
              <Grid.Row>
                <Table compact>
                  <Table.Header className="mobile hidden">
                    <Table.Row className="small-header">
                      <Table.HeaderCell></Table.HeaderCell>
                      <Table.HeaderCell>Closing Date</Table.HeaderCell>
                      <Table.HeaderCell>Agent</Table.HeaderCell>
                      <Table.HeaderCell>
                        Client&nbsp;&nbsp;(Primary)
                      </Table.HeaderCell>
                      <Table.HeaderCell>
                        Client&nbsp;&nbsp;(Secondary)
                      </Table.HeaderCell>
                      <Table.HeaderCell>Representing</Table.HeaderCell>
                      <Table.HeaderCell>Address</Table.HeaderCell>
                    </Table.Row>
                  </Table.Header>
                  <Table.Body>
                    {sortedTransactions.map((transaction) => (
                      <ManagerTransactionsListItem
                        transaction={transaction}
                        key={transaction.id}
                        useDate={transaction.closingDateTime}
                      />
                    ))}
                  </Table.Body>
                </Table>
              </Grid.Row>
            ) : (
              <Grid.Row>
                <p>
                  Contact TransActioner to subscribe to Manager Transaction
                  Lists.
                </p>
              </Grid.Row>
            )}
          </Grid>
        </div>
      </div>
    </>
  );
}
