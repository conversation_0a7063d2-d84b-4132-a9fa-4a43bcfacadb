import React, { useState } from "react";
import { useDispatch } from "react-redux";
import { toast } from "react-toastify";
import { Confirm, Dropdown } from "semantic-ui-react";
import { openModal } from "../../../app/common/modals/modalSlice";
import { useMediaQuery } from "react-responsive";
import { deletePersonInDb } from "../../../app/firestore/firestoreService";

export default function PeopleActionButtons({ person }) {
  const dispatch = useDispatch();
  const [confirmOpen, setConfirmOpen] = useState(false);
  const isMobile = useMediaQuery({ query: "(max-width:768px)" });

  function handleView() {
    dispatch(
      openModal({
        modalType: "PersonView",
        modalProps: { person: person },
      })
    );
  }

  function handleEdit() {
    dispatch(
      openModal({
        modalType: "PersonForm",
        modalProps: { person: person },
      })
    );
  }

  async function handleDelete(person) {
    try {
      await deletePersonInDb(person.id);
      setConfirmOpen(false);
      toast.success("Person successfully deleted");
    } catch (error) {
      toast.error(error.message);
    }
  }

  return (
    <div className={isMobile ? null : "text align right"}>
      <Dropdown
        button
        icon="chevron down"
        text={isMobile ? "Actions " : null}
        className={isMobile ? "button icon" : "button mini icon"}
        direction={isMobile ? "right" : "left"}
      >
        <Dropdown.Menu>
          <Dropdown.Item onClick={() => handleView()}>View</Dropdown.Item>
          <Dropdown.Item onClick={() => handleEdit()}>Edit</Dropdown.Item>
          <Dropdown.Item onClick={() => setConfirmOpen(true)}>
            Delete
          </Dropdown.Item>
        </Dropdown.Menu>
      </Dropdown>
      <Confirm
        open={confirmOpen}
        onCancel={() => setConfirmOpen(false)}
        onConfirm={() => handleDelete(person)}
      ></Confirm>
    </div>
  );
}
