import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Head<PERSON>, Segment } from "semantic-ui-react";
import { useMediaQuery } from "react-responsive";
import TaskTemplatesList from "./TaskTemplatesList";
import ModalWrapper from "../../../../app/common/modals/modalWrapper";
import { closeModal } from "../../../../app/common/modals/modalSlice";
import {
  fetchManagerTaskTemplatesFromDb,
  fetchTaskTemplatesFromDb,
} from "../../../../app/firestore/firestoreService";
import { fetchTaskTemplates } from "../taskTemplatesSlice";
import useFirestoreCollectionNoAsync from "../../../../app/hooks/useFirestoreCollectionNoAsync";
import { onSnapshot } from "firebase/firestore";

export default function TaskTemplatesSelect() {
  const dispatch = useDispatch();
  const { taskTemplates } = useSelector((state) => state.taskTemplates);
  const { currentUserProfile } = useSelector((state) => state.profile);
  const [managerTemplates, setManagerTemplates] = useState([]);
  const isMobile = useMediaQuery({ query: "(max-width:768px)" });

  useFirestoreCollectionNoAsync({
    query: () => fetchTaskTemplatesFromDb(),
    data: (userTemplates) => {
      const templatesWithSource = userTemplates.map((template) => ({
        ...template,
        templateSource: "own",
      }));

      // Combine with manager templates
      const allTemplates = [...templatesWithSource, ...managerTemplates];
      dispatch(fetchTaskTemplates(allTemplates));
    },
    deps: [dispatch, managerTemplates],
  });

  // Fetch manager templates if user is an agent with a manager
  useEffect(() => {
    if (currentUserProfile?.role === "agent" && currentUserProfile?.managerId) {
      const unsubscribe = onSnapshot(
        fetchManagerTaskTemplatesFromDb(currentUserProfile.managerId),
        (snapshot) => {
          const managerTemplatesData = snapshot.docs.map((doc) => ({
            ...doc.data(),
            id: doc.id,
            templateSource: "manager",
          }));
          setManagerTemplates(managerTemplatesData);
        },
        (error) => {
          console.error("Error fetching manager templates:", error);
        }
      );

      return () => unsubscribe();
    } else {
      setManagerTemplates([]);
    }
  }, [currentUserProfile]);

  function handleClose() {
    dispatch(
      closeModal({
        modalType: "TaskTemplatesSelect",
      })
    );
  }

  return (
    <>
      <ModalWrapper style={{ backgroundColor: "#f9fafb" }}>
        <Segment clearing style={{ backgroundColor: "#f9fafb" }}>
          <Header color="blue" size="large">
            Select Task Template to Add
          </Header>
          <Divider />{" "}
          {taskTemplates?.length > 0 ? (
            <TaskTemplatesList
              taskTemplates={taskTemplates}
              column={taskTemplates.column}
              direction={taskTemplates.direction}
              actionButtonMode="apply"
            />
          ) : (
            <p>There are no task templates.</p>
          )}
          <Button
            floated={isMobile ? null : "right"}
            content="Close"
            onClick={() => handleClose()}
            className={isMobile ? "fluid" : null}
          ></Button>
        </Segment>
      </ModalWrapper>
    </>
  );
}
