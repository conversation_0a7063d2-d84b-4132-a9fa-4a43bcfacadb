import React, { useEffect } from "react";
import { Button, Grid, Input } from "semantic-ui-react";
import { useDispatch, useSelector } from "react-redux";
import { useState } from "react";
import TransactionUnderContractList from "./transactionUnderContract/TransactionUnderContractList";
import TransactionActiveListingList from "./transactionActiveListing/TransactionActiveListingList";
import TransactionActiveBuyerList from "./transactionActiveBuyer/TransactionActiveBuyerList";
import { Link, useParams } from "react-router-dom";
import { searchFilter } from "../../../app/common/util/util";
import { openModal } from "../../../app/common/modals/modalSlice";
import LoadingComponent from "../../../app/layout/LoadingComponent";
import { resetTransAll } from "../transactionSlice";
import { useMediaQuery } from "react-responsive";

export default function TransactionActiveDashboard() {
  const dispatch = useDispatch();
  const { currentUserProfile } = useSelector((state) => state.profile);
  const { transUnderContract, transActiveListing, transActiveBuyer } =
    useSelector((state) => state.transaction);
  const [searchTerms, setSearchTerms] = useState("");
  let { agentId } = useParams();
  const isMobile = useMediaQuery({ query: "(max-width:850px)" });

  // Reset transAll state when component mounts to avoid stale data interfering with other dashboards
  useEffect(() => {
    dispatch(resetTransAll());
  }, [dispatch]);

  const transUnderContractFiltered = searchFilter(
    transUnderContract?.transactions,
    searchTerms
  );
  const transActiveListingFiltered = searchFilter(
    transActiveListing?.transactions,
    searchTerms
  );
  const transActiveBuyerFiltered = searchFilter(
    transActiveBuyer?.transactions,
    searchTerms
  );

  if (!(transUnderContract && transActiveListing && transActiveBuyer)) {
    return <LoadingComponent content="Loading transactions..." />;
  }

  return (
    <div className="main-page-wrapper">
      <Grid stackable>
        <Grid.Column computer={5} tablet={16}>
          <Input
            type="text"
            fluid
            size="small"
            placeholder="Search by client name or address"
            value={searchTerms}
            onChange={(e) => setSearchTerms(e.target.value)}
          ></Input>
        </Grid.Column>
        <Grid.Column computer={5} tablet={8}>
          <Button.Group fluid size="small">
            <Button active as={Link} to="">
              Active
            </Button>
            <Button
              as={Link}
              to={
                currentUserProfile?.role === "manager" ||
                currentUserProfile?.role === "managerassistant"
                  ? `/agent/${agentId}/transactionsAll`
                  : "/transactionsAll"
              }
            >
              All
            </Button>
            <Button
              as={Link}
              to={
                currentUserProfile?.role === "manager" ||
                currentUserProfile?.role === "managerassistant"
                  ? `/agent/${agentId}/transactionsComplete`
                  : "/transactionsComplete"
              }
            >
              Complete
            </Button>
            <Button
              as={Link}
              to={
                currentUserProfile?.role === "manager" ||
                currentUserProfile?.role === "managerassistant"
                  ? `/agent/${agentId}/transactionsArchived`
                  : "/transactionsArchived"
              }
            >
              Archived
            </Button>
          </Button.Group>
        </Grid.Column>
        <Grid.Column computer={4} tablet={4}>
          {(currentUserProfile.role === "agent" ||
            currentUserProfile.role === "assistant") && (
            <Button
              color="blue"
              to="#"
              floated={isMobile ? null : "right"}
              icon="plus"
              size="small"
              className={isMobile ? "fluid" : null}
              onClick={() =>
                dispatch(
                  openModal({
                    modalType: "TransactionForm",
                  })
                )
              }
              content="New Transaction"
            />
          )}
        </Grid.Column>
        <Grid.Column computer={16}>
          <h3 className="mini bottom margin">Under Contract</h3>
          {transUnderContractFiltered?.length > 0 ? (
            <TransactionUnderContractList
              transactions={transUnderContractFiltered}
              column={transUnderContract.column}
              direction={transUnderContract.direction}
            />
          ) : (
            <p>There are no transactions under contract</p>
          )}

          <h3 className="mini bottom margin">Active Listings</h3>
          {transActiveListingFiltered?.length > 0 ? (
            <TransactionActiveListingList
              transactions={transActiveListingFiltered}
              column={transActiveListing.column}
              direction={transActiveListing.direction}
            />
          ) : (
            <p>There are no active listings</p>
          )}

          <h3 className="mini bottom margin">Active Buyers</h3>
          {transActiveBuyerFiltered?.length > 0 ? (
            <TransactionActiveBuyerList
              transactions={transActiveBuyerFiltered}
              column={transActiveBuyer.column}
              direction={transActiveBuyer.direction}
            />
          ) : (
            <p>There are no active buyers</p>
          )}
        </Grid.Column>
      </Grid>
    </div>
  );
}
