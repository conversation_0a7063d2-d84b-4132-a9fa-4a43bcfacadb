import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Segment } from "semantic-ui-react";
import { useMediaQuery } from "react-responsive";
import { Formik, Form } from "formik";
import { toast } from "react-toastify";
import { closeModal } from "../../../app/common/modals/modalSlice";
import ModalWrapper from "../../../app/common/modals/modalWrapper";
import {
  functionLinkTransactionWithAgent,
} from "../../../app/firestore/functionsService";
import MyTextInput from "../../../app/common/form/MyTextInput";
import MyTextArea from "../../../app/common/form/MyTextArea";
import {
  updatePartyInDb,
} from "../../../app/firestore/firestoreService";

export default function TransactionLinkingModal({ party, transaction }) {
  const { currentUserProfile } = useSelector((state) => state.profile);
  const isMobile = useMediaQuery({ query: "(max-width:768px)" });
  const dispatch = useDispatch();

  let initialValues = {
    ...transaction,
    mlsNumbers: transaction.mlsNumbers || ["", ""],
    propertyDetails: {
      ...transaction.propertyDetails,
      legalDescription: transaction.propertyDetails?.legalDescription || "",
    },
  };

  function handleSkipLinking() {
    // Mark party as declined linking to prevent future prompts
    updatePartyInDb(party, { declinedLinking: true });
    toast.success("Linking skipped. You can link transactions later from the Documents page.");
    dispatch(
      closeModal({
        modalType: "TransactionLinkingModal",
      })
    );
  }

  return (
    <>
      <ModalWrapper size="small">
        <Segment>
          <Grid>
            <Grid.Column>
              <Header size="large" color="blue">
                Link Transaction
              </Header>
              <Divider />
              <p className="text-large text blue bold ">
                Good news! {party.firstName} {party.lastName} is also using
                TransActioner.
              </p>
              <p className="text-medium small bottom margin">
                We can link your transactions so documents show up automatically
                in both transactions. Please verify the MLS number(s) and
                legal description below are correct and then hit the "Link
                Transaction" button.
              </p>
              <p className="text-medium tiny bottom margin">
                If we aren't able to link the transaction based on the info
                below, you can click "Skip Linking" and try again later from
                the Documents page when sharing documents.
              </p>
              <Formik
                initialValues={initialValues}
                onSubmit={async (values, { setSubmitting }) => {
                  try {
                    setSubmitting(true);
                    const result = await functionLinkTransactionWithAgent({
                      party: party,
                      values: values,
                    });
                    if (result?.error) {
                      toast.error(result.error);
                    } else {
                      setSubmitting(false);
                      await updatePartyInDb(party, {
                        isLinked: true,
                        linkedTransactionId: result.transactionId,
                      });
                      toast.success("Transaction successfully linked!");
                      dispatch(
                        closeModal({
                          modalType: "TransactionLinkingModal",
                        })
                      );
                    }
                  } catch (error) {
                    toast.error(error.message);
                    setSubmitting(false);
                  }
                }}
              >
                {({ isSubmitting, values }) => (
                  <Form className="ui form">
                    <Grid>
                      <Grid.Row>
                        <Grid.Column mobile={16} computer={8}>
                          <MyTextInput
                            name="mlsNumbers[0]"
                            label="MLS Number"
                            value={
                              values.mlsNumbers && values.mlsNumbers[0]
                                ? values.mlsNumbers[0]
                                : ""
                            }
                          />
                        </Grid.Column>
                        <Grid.Column mobile={16} computer={8}>
                          <MyTextInput
                            name="mlsNumbers[1]"
                            label="MLS Number 2 (if applicable)"
                            value={
                              values.mlsNumbers && values.mlsNumbers[1]
                                ? values.mlsNumbers[1]
                                : ""
                            }
                          />
                        </Grid.Column>
                      </Grid.Row>
                      <Grid.Row>
                        <Grid.Column mobile={16} computer={16}>
                          <MyTextArea
                            name="propertyDetails.legalDescription"
                            label="Legal Description"
                            rows={3}
                          />
                        </Grid.Column>
                      </Grid.Row>
                    </Grid>
                    <Divider />
                    <Button
                      onClick={() => handleSkipLinking()}
                      floated={isMobile ? null : "right"}
                      content="Skip Linking"
                      className={isMobile ? "fluid medium bottom margin" : null}
                    />
                    <Button
                      loading={isSubmitting}
                      disabled={isSubmitting}
                      type="submit"
                      floated={isMobile ? null : "right"}
                      primary
                      content="Link Transaction"
                      className={isMobile ? "fluid medium bottom margin" : null}
                    />
                    <Button
                      disabled={isSubmitting}
                      onClick={() =>
                        dispatch(
                          closeModal({
                            modalType: "TransactionLinkingModal",
                          })
                        )
                      }
                      to="#"
                      type="button"
                      floated={isMobile ? null : "right"}
                      content="Cancel"
                      className={isMobile ? "fluid medium" : null}
                    />
                  </Form>
                )}
              </Formik>
            </Grid.Column>
          </Grid>
        </Segment>
      </ModalWrapper>
    </>
  );
}
